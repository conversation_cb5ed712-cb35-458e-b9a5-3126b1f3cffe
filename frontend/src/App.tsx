/**
 * Main App component with routing and API integration
 */
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { loginUser, registerUser, logoutUser, checkAuth, selectAuth } from './store/authSlice';
import { chatService } from './services';
import type { AppDispatch } from './store';
import ProtectedRoute from './components/ProtectedRoute';



// Login Page with modern design and animations
const LoginPage = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading, error } = useSelector(selectAuth);
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('demo123');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const result = await dispatch(loginUser({ email, password })).unwrap();
      if (result.success) {
        window.location.href = '/dashboard';
      }
    } catch (err: any) {
      // Error is handled by Redux
      console.error('Login failed:', err);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 flex items-center justify-center p-4">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-black opacity-5 rounded-full animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-black opacity-5 rounded-full animate-pulse delay-1000"></div>
      </div>

      <div className={`relative max-w-md w-full transition-all duration-1000 ${mounted ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
        {/* Main card */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-2xl border border-gray-200/50 p-8 space-y-8">
          {/* Header with animation */}
          <div className="text-center space-y-2">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-black rounded-2xl mb-4 transform transition-transform hover:scale-110">
              <span className="text-white text-2xl">💬</span>
            </div>
            <h2 className="text-3xl font-bold text-black tracking-tight">Welcome Back</h2>
            <p className="text-gray-600">Sign in to your Chat System account</p>
          </div>

          {/* Error message with animation */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl animate-shake">
              <div className="flex items-center space-x-2">
                <span>⚠️</span>
                <span>{error}</span>
              </div>
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleLogin} className="space-y-6">
            <div className="space-y-4">
              <div className="group">
                <label className="block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-black">
                  Email Address
                </label>
                <div className="relative">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                    required
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                    <span className="text-gray-400">📧</span>
                  </div>
                </div>
              </div>

              <div className="group">
                <label className="block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-black">
                  Password
                </label>
                <div className="relative">
                  <input
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                    required
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                    <span className="text-gray-400">🔒</span>
                  </div>
                </div>
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-black text-white py-3 px-4 rounded-xl font-medium hover:bg-gray-800 disabled:opacity-50 transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] disabled:hover:scale-100"
            >
              {isLoading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Signing In...</span>
                </div>
              ) : (
                'Sign In'
              )}
            </button>
          </form>

          {/* Footer */}
          <div className="text-center space-y-4">
            <p className="text-gray-600">
              Don't have an account?{' '}
              <a href="/signup" className="text-black hover:underline font-medium transition-colors">
                Sign up
              </a>
            </p>

            {/* Demo credentials card */}
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 border border-gray-200">
              <div className="text-sm text-gray-600">
                <div className="flex items-center justify-center space-x-2 mb-2">
                  <span>🎯</span>
                  <strong>Demo Credentials</strong>
                </div>
                <div className="space-y-1">
                  <div>📧 <EMAIL></div>
                  <div>🔑 demo123</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Signup Page with modern design and animations
const SignupPage = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading, error } = useSelector(selectAuth);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    password: ''
  });
  const [mounted, setMounted] = useState(false);
  const [step, setStep] = useState(1);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const result = await dispatch(registerUser(formData)).unwrap();
      if (result.success) {
        window.location.href = '/dashboard';
      }
    } catch (err: any) {
      // Error is handled by Redux
      console.error('Registration failed:', err);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const nextStep = () => setStep(2);
  const prevStep = () => setStep(1);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 flex items-center justify-center p-4">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -left-40 w-80 h-80 bg-black opacity-5 rounded-full animate-pulse delay-500"></div>
        <div className="absolute -bottom-40 -right-40 w-80 h-80 bg-black opacity-5 rounded-full animate-pulse delay-1500"></div>
      </div>

      <div className={`relative max-w-md w-full transition-all duration-1000 ${mounted ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
        {/* Main card */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-2xl border border-gray-200/50 p-8 space-y-8">
          {/* Header with animation */}
          <div className="text-center space-y-2">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-black rounded-2xl mb-4 transform transition-transform hover:scale-110">
              <span className="text-white text-2xl">🚀</span>
            </div>
            <h2 className="text-3xl font-bold text-black tracking-tight">Join Us</h2>
            <p className="text-gray-600">Create your Chat System account</p>

            {/* Progress indicator */}
            <div className="flex justify-center space-x-2 mt-4">
              <div className={`w-3 h-3 rounded-full transition-all duration-300 ${step >= 1 ? 'bg-black' : 'bg-gray-300'}`}></div>
              <div className={`w-3 h-3 rounded-full transition-all duration-300 ${step >= 2 ? 'bg-black' : 'bg-gray-300'}`}></div>
            </div>
          </div>

          {/* Error message with animation */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl animate-shake">
              <div className="flex items-center space-x-2">
                <span>⚠️</span>
                <span>{error}</span>
              </div>
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleSignup} className="space-y-6">
            {step === 1 && (
              <div className="space-y-4 animate-fadeIn">
                <div className="group">
                  <label className="block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-black">
                    Full Name
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                      required
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                      <span className="text-gray-400">👤</span>
                    </div>
                  </div>
                </div>

                <div className="group">
                  <label className="block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-black">
                    Email Address
                  </label>
                  <div className="relative">
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                      required
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                      <span className="text-gray-400">📧</span>
                    </div>
                  </div>
                </div>

                <button
                  type="button"
                  onClick={nextStep}
                  className="w-full bg-black text-white py-3 px-4 rounded-xl font-medium hover:bg-gray-800 transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
                >
                  Continue
                </button>
              </div>
            )}

            {step === 2 && (
              <div className="space-y-4 animate-fadeIn">
                <div className="group">
                  <label className="block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-black">
                    Phone Number
                  </label>
                  <div className="relative">
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                      required
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                      <span className="text-gray-400">📱</span>
                    </div>
                  </div>
                </div>

                <div className="group">
                  <label className="block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-black">
                    Password
                  </label>
                  <div className="relative">
                    <input
                      type="password"
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                      required
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                      <span className="text-gray-400">🔒</span>
                    </div>
                  </div>
                </div>

                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={prevStep}
                    className="flex-1 bg-gray-100 text-gray-700 py-3 px-4 rounded-xl font-medium hover:bg-gray-200 transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
                  >
                    Back
                  </button>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="flex-1 bg-black text-white py-3 px-4 rounded-xl font-medium hover:bg-gray-800 disabled:opacity-50 transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] disabled:hover:scale-100"
                  >
                    {isLoading ? (
                      <div className="flex items-center justify-center space-x-2">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>Creating...</span>
                      </div>
                    ) : (
                      'Create Account'
                    )}
                  </button>
                </div>
              </div>
            )}
          </form>

          {/* Footer */}
          <div className="text-center">
            <p className="text-gray-600">
              Already have an account?{' '}
              <a href="/login" className="text-black hover:underline font-medium transition-colors">
                Sign in
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Dashboard Layout with Sidebar
const DashboardLayout = ({ children }: { children: React.ReactNode }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { user, isAuthenticated, isLoading } = useSelector(selectAuth);

  useEffect(() => {
    // Check authentication on mount
    if (!isAuthenticated && !isLoading) {
      dispatch(checkAuth());
    }
  }, [dispatch, isAuthenticated, isLoading]);

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!isLoading && !isAuthenticated) {
      window.location.href = '/login';
    }
  }, [isAuthenticated, isLoading]);

  const handleLogout = async () => {
    try {
      await dispatch(logoutUser()).unwrap();
      window.location.href = '/login';
    } catch (error) {
      // Force logout even if API call fails
      window.location.href = '/login';
    }
  };

  if (isLoading || !user) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-lg">
        <div className="p-6">
          <h1 className="text-xl font-bold text-black">Chat System</h1>
          <p className="text-sm text-gray-600 mt-1">Welcome, {user.name}</p>
        </div>
        <nav className="mt-6">
          <a
            href="/dashboard"
            className="block px-6 py-3 text-gray-700 hover:bg-gray-100 border-r-2 border-transparent hover:border-black"
          >
            📊 Dashboard
          </a>
          <a
            href="/chat"
            className="block px-6 py-3 text-gray-700 hover:bg-gray-100 border-r-2 border-transparent hover:border-black"
          >
            💬 Chat Playground
          </a>
          <a
            href="/booking"
            className="block px-6 py-3 text-gray-700 hover:bg-gray-100 border-r-2 border-transparent hover:border-black"
          >
            📅 Booking
          </a>
          <a
            href="/cta"
            className="block px-6 py-3 text-gray-700 hover:bg-gray-100 border-r-2 border-transparent hover:border-black"
          >
            🎯 CTA Page
          </a>
          <button
            onClick={handleLogout}
            className="block w-full text-left px-6 py-3 text-gray-700 hover:bg-gray-100 border-r-2 border-transparent hover:border-black"
          >
            🚪 Logout
          </button>
        </nav>
      </div>

      {/* Main content */}
      <div className="flex-1 p-8">
        {children}
      </div>
    </div>
  );
};

// Animated Stat Card Component
const StatCard = ({ icon, title, value, delay = 0, loading = false }: {
  icon: string;
  title: string;
  value: number;
  delay?: number;
  loading?: boolean;
}) => {
  const [mounted, setMounted] = useState(false);
  const [animatedValue, setAnimatedValue] = useState(0);

  useEffect(() => {
    setTimeout(() => setMounted(true), delay);

    if (!loading && value > 0) {
      const duration = 2000;
      const steps = 60;
      const increment = value / steps;
      let current = 0;

      const timer = setInterval(() => {
        current += increment;
        if (current >= value) {
          setAnimatedValue(value);
          clearInterval(timer);
        } else {
          setAnimatedValue(Math.floor(current));
        }
      }, duration / steps);

      return () => clearInterval(timer);
    }
  }, [value, delay, loading]);

  if (loading) {
    return (
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-6 animate-pulse">
        <div className="flex items-center">
          <div className="p-3 rounded-2xl bg-gray-200 w-12 h-12"></div>
          <div className="ml-4 flex-1">
            <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
            <div className="h-8 bg-gray-200 rounded w-16"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-6 transform transition-all duration-700 hover:scale-105 hover:shadow-xl ${
      mounted ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
    }`}>
      <div className="flex items-center">
        <div className="p-3 rounded-2xl bg-gradient-to-br from-gray-100 to-gray-200 transform transition-transform hover:rotate-12">
          <span className="text-2xl">{icon}</span>
        </div>
        <div className="ml-4">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-3xl font-bold text-black">
            {animatedValue.toLocaleString()}
          </p>
        </div>
      </div>
    </div>
  );
};

// Dashboard Page with animated cards
const DashboardPage = () => {
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const { dashboardService } = await import('./services');
        const response = await dashboardService.getDashboardStats();
        setStats(response);
      } catch (error) {
        console.error('Failed to fetch dashboard stats:', error);
      } finally {
        setTimeout(() => setLoading(false), 1000); // Add delay for better UX
      }
    };

    fetchStats();
  }, []);

  const statsData = [
    { icon: '👥', title: 'Total Users', value: stats?.total_users || 0, delay: 100 },
    { icon: '💬', title: 'Total Messages', value: stats?.total_messages || 0, delay: 200 },
    { icon: '📅', title: 'Total Bookings', value: stats?.total_bookings || 0, delay: 300 },
    { icon: '🔥', title: 'Active Sessions', value: stats?.active_sessions || 0, delay: 400 },
    { icon: '📈', title: "Today's Messages", value: stats?.today_messages || 0, delay: 500 },
    { icon: '📊', title: "Today's Bookings", value: stats?.today_bookings || 0, delay: 600 },
  ];

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header with animation */}
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-bold text-black mb-2 animate-fadeIn">Dashboard</h1>
          <p className="text-gray-600 animate-fadeIn delay-200">Overview of your chat system performance</p>
        </div>

        {/* Stats Grid with animated cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {statsData.map((stat) => (
            <StatCard
              key={stat.title}
              icon={stat.icon}
              title={stat.title}
              value={stat.value}
              delay={stat.delay}
              loading={loading}
            />
          ))}
        </div>

        {/* Quick Actions with enhanced design */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8 animate-fadeIn delay-700">
          <h2 className="text-2xl font-bold text-black mb-6 text-center">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <a
              href="/chat"
              className="group p-6 border border-gray-200 rounded-2xl hover:bg-gradient-to-br hover:from-gray-50 hover:to-white text-center transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
            >
              <div className="text-4xl mb-4 transform transition-transform group-hover:scale-110 group-hover:rotate-12">💬</div>
              <span className="font-semibold text-gray-800 group-hover:text-black">Start Chat</span>
              <p className="text-sm text-gray-500 mt-2">Begin a conversation with AI</p>
            </a>
            <a
              href="/booking"
              className="group p-6 border border-gray-200 rounded-2xl hover:bg-gradient-to-br hover:from-gray-50 hover:to-white text-center transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
            >
              <div className="text-4xl mb-4 transform transition-transform group-hover:scale-110 group-hover:rotate-12">📅</div>
              <span className="font-semibold text-gray-800 group-hover:text-black">View Bookings</span>
              <p className="text-sm text-gray-500 mt-2">Manage appointments</p>
            </a>
            <a
              href="/cta"
              className="group p-6 border border-gray-200 rounded-2xl hover:bg-gradient-to-br hover:from-gray-50 hover:to-white text-center transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
            >
              <div className="text-4xl mb-4 transform transition-transform group-hover:scale-110 group-hover:rotate-12">🎯</div>
              <span className="font-semibold text-gray-800 group-hover:text-black">CTA Page</span>
              <p className="text-sm text-gray-500 mt-2">Explore features</p>
            </a>
          </div>
        </div>

        {/* Performance indicator */}
        <div className="mt-8 text-center animate-fadeIn delay-1000">
          <div className="inline-flex items-center space-x-2 bg-green-50 text-green-700 px-4 py-2 rounded-full border border-green-200">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium">System Running Smoothly</span>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

// Modern Message Bubble Component
const MessageBubble = ({ message, index }: { message: any; index: number }) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setTimeout(() => setMounted(true), index * 50);
  }, [index]);

  const isUser = message.message_type === 'user';
  const isError = message.message_type === 'error';

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} ${mounted ? 'animate-fadeIn' : 'opacity-0'}`}>
      <div className={`flex items-end space-x-3 max-w-2xl ${isUser ? 'flex-row-reverse space-x-reverse' : ''}`}>

        {/* Avatar */}
        {!isUser && (
          <div className="w-10 h-10 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center flex-shrink-0">
            <span className="text-lg">{isError ? '⚠️' : '🤖'}</span>
          </div>
        )}

        {isUser && (
          <div className="w-10 h-10 bg-gradient-to-br from-black to-gray-700 rounded-2xl flex items-center justify-center flex-shrink-0">
            <span className="text-white text-lg">👤</span>
          </div>
        )}

        {/* Message Content */}
        <div className={`group relative ${isUser ? 'ml-12' : 'mr-12'}`}>
          <div className={`px-6 py-4 rounded-3xl shadow-lg transform transition-all duration-300 hover:scale-[1.02] ${
            isUser
              ? 'bg-gradient-to-br from-black to-gray-700 text-white rounded-br-lg'
              : isError
              ? 'bg-gradient-to-br from-red-50 to-red-100 text-red-800 border border-red-200 rounded-bl-lg'
              : 'bg-white text-gray-900 border border-gray-200 rounded-bl-lg'
          }`}>

            {/* Message Text */}
            <div className="text-sm leading-relaxed whitespace-pre-wrap">
              {message.content}
            </div>

            {/* Timestamp */}
            <div className={`text-xs mt-3 flex items-center ${
              isUser ? 'text-gray-300' : 'text-gray-500'
            }`}>
              <span className="mr-1">
                {new Date(message.timestamp).toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </span>
              {isUser && (
                <span className="ml-2 text-xs">
                  ✓
                </span>
              )}
            </div>
          </div>

          {/* Message tail */}
          <div className={`absolute top-4 ${
            isUser
              ? 'right-0 translate-x-2'
              : 'left-0 -translate-x-2'
          }`}>
            <div className={`w-4 h-4 transform rotate-45 ${
              isUser
                ? 'bg-gradient-to-br from-black to-gray-700'
                : isError
                ? 'bg-gradient-to-br from-red-50 to-red-100 border-l border-t border-red-200'
                : 'bg-white border-l border-t border-gray-200'
            }`}></div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Chat Playground with modern design and animations
const ChatPlayground = () => {
  const [messages, setMessages] = useState<any[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [sessionId] = useState(() => chatService.getOrCreateSessionId());
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || loading) return;

    const userMessage = {
      content: newMessage,
      message_type: 'user',
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setNewMessage('');
    setLoading(true);
    setIsTyping(true);

    try {
      const response = await chatService.sendMessage({
        message: userMessage.content,
        session_id: sessionId
      });

      setTimeout(() => {
        if (response) {
          const assistantMessage = {
            content: response.response,
            message_type: 'assistant',
            timestamp: new Date().toISOString()
          };

          setMessages(prev => [...prev, assistantMessage]);
        }
        setIsTyping(false);
      }, 1000);
    } catch (error: any) {
      const errorMessage = {
        content: `Error: ${error.message}`,
        message_type: 'error',
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, errorMessage]);
      setIsTyping(false);
    } finally {
      setLoading(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="h-full flex flex-col">
        {/* Modern Chat Container - Full Height */}
        <div className="flex-1 flex flex-col bg-gradient-to-br from-gray-50 to-white rounded-3xl shadow-2xl border border-gray-200/50 overflow-hidden animate-fadeIn">

          {/* Chat Header - Modern Design */}
          <div className="bg-white/90 backdrop-blur-sm px-6 py-4 border-b border-gray-200/50 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-br from-black to-gray-700 rounded-2xl flex items-center justify-center shadow-lg">
                  <span className="text-white text-xl">🤖</span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white animate-pulse"></div>
              </div>
              <div>
                <h3 className="font-bold text-gray-900 text-lg">AI Assistant</h3>
                <p className="text-sm text-gray-500 flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></span>
                  Online • Ready to help
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <div className="px-3 py-1 bg-green-50 text-green-700 rounded-full text-xs font-medium border border-green-200">
                {messages.length} messages
              </div>
            </div>
          </div>

          {/* Chat Messages Area */}
          <div className="flex-1 overflow-y-auto p-6 bg-gradient-to-b from-gray-50/20 to-transparent">
            {messages.length === 0 ? (
              <div className="h-full flex items-center justify-center">
                <div className="text-center max-w-md animate-fadeIn">
                  <div className="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl flex items-center justify-center mb-6 mx-auto animate-float">
                    <span className="text-4xl">💬</span>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">Start a conversation</h3>
                  <p className="text-gray-500 mb-8 leading-relaxed">
                    I'm your AI assistant, ready to help with questions, provide information, and assist with bookings.
                  </p>

                  {/* Quick Start Suggestions */}
                  <div className="space-y-3">
                    <p className="text-sm font-medium text-gray-700 mb-4">Try asking:</p>
                    <div className="grid gap-3">
                      {[
                        { text: 'What services do you offer?', icon: '🔍' },
                        { text: 'Show me available time slots', icon: '📅' },
                        { text: 'How can I book an appointment?', icon: '📝' }
                      ].map((suggestion, i) => (
                        <button
                          key={i}
                          onClick={() => setNewMessage(suggestion.text)}
                          className="flex items-center space-x-3 w-full p-4 bg-white border border-gray-200 rounded-2xl hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 transform hover:scale-[1.02] text-left"
                        >
                          <span className="text-2xl">{suggestion.icon}</span>
                          <span className="text-gray-700 font-medium">{suggestion.text}</span>
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {messages.map((message, index) => (
                  <MessageBubble key={index} message={message} index={index} />
                ))}

                {/* Typing Indicator */}
                {isTyping && (
                  <div className="flex justify-start animate-fadeIn">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center">
                        <span className="text-lg">🤖</span>
                      </div>
                      <div className="bg-white border border-gray-200 px-6 py-4 rounded-3xl rounded-bl-lg shadow-lg">
                        <div className="flex items-center space-x-2">
                          <div className="flex space-x-1">
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce delay-100"></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce delay-200"></div>
                          </div>
                          <span className="text-sm text-gray-600 ml-2">AI is thinking...</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>
            )}
          </div>

          {/* Message Input Area - Modern Design */}
          <div className="bg-white/90 backdrop-blur-sm border-t border-gray-200/50 p-6">
            <form onSubmit={sendMessage} className="flex items-end space-x-4">
              <div className="flex-1 relative">
                <div className="relative">
                  <textarea
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendMessage(e);
                      }
                    }}
                    placeholder="Type your message... (Press Enter to send)"
                    className="w-full px-6 py-4 pr-12 border border-gray-300 rounded-3xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white resize-none min-h-[56px] max-h-32"
                    disabled={loading}
                    rows={1}
                  />
                  <div className="absolute bottom-4 right-4 text-gray-400">
                    <span className="text-lg">💭</span>
                  </div>
                </div>
              </div>

              <button
                type="submit"
                disabled={loading || !newMessage.trim()}
                className="w-14 h-14 bg-gradient-to-br from-black to-gray-700 text-white rounded-2xl font-medium hover:from-gray-700 hover:to-gray-800 disabled:opacity-50 transition-all duration-200 transform hover:scale-105 active:scale-95 disabled:hover:scale-100 flex items-center justify-center shadow-lg"
              >
                {loading ? (
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <span className="text-xl">🚀</span>
                )}
              </button>
            </form>

            {/* Input Helper Text */}
            <div className="mt-3 flex items-center justify-between text-xs text-gray-500">
              <span>Press Enter to send, Shift+Enter for new line</span>
              <span>{newMessage.length}/1000</span>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

// Feature Card Component
const FeatureCard = ({ icon, title, description, delay = 0 }: {
  icon: string;
  title: string;
  description: string;
  delay?: number;
}) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setTimeout(() => setMounted(true), delay);
  }, [delay]);

  return (
    <div className={`group text-center transform transition-all duration-700 ${mounted ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8 h-full hover:shadow-xl transition-all duration-300 transform hover:scale-105">
        <div className="text-6xl mb-6 transform transition-transform group-hover:scale-110 group-hover:rotate-12 animate-float">
          {icon}
        </div>
        <h3 className="text-2xl font-bold mb-4 text-black">{title}</h3>
        <p className="text-gray-600 leading-relaxed">{description}</p>
      </div>
    </div>
  );
};

// CTA Page with modern design
const CTAPage = () => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const features = [
    {
      icon: '🚀',
      title: 'Advanced AI',
      description: 'Powered by cutting-edge language models with LangGraph integration for complex reasoning and tool usage.',
      delay: 200
    },
    {
      icon: '📊',
      title: 'Analytics',
      description: 'Comprehensive insights and performance metrics to track your chat system\'s effectiveness and user engagement.',
      delay: 400
    },
    {
      icon: '🔒',
      title: 'Secure',
      description: 'Enterprise-grade security with session-based authentication and encrypted data transmission.',
      delay: 600
    }
  ];

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className={`transition-all duration-1000 ${mounted ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <h1 className="text-6xl font-bold text-black mb-6 leading-tight">
              Ready to Transform
              <br />
              <span className="bg-gradient-to-r from-gray-600 to-black bg-clip-text text-transparent">
                Your Chat Experience?
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
              Join thousands of users who have revolutionized their communication with our AI-powered chat system.
              Experience the future of intelligent conversations today.
            </p>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
              delay={feature.delay}
            />
          ))}
        </div>

        {/* Stats Section */}
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-gray-200/50 p-12 mb-16 animate-fadeIn delay-700">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-black mb-2">10K+</div>
              <div className="text-gray-600">Active Users</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-black mb-2">1M+</div>
              <div className="text-gray-600">Messages Processed</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-black mb-2">99.9%</div>
              <div className="text-gray-600">Uptime</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-black mb-2">24/7</div>
              <div className="text-gray-600">Support</div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center animate-fadeIn delay-1000">
          <div className="bg-gradient-to-r from-black to-gray-800 rounded-3xl p-12 text-white">
            <h2 className="text-4xl font-bold mb-6">Start Your Journey Today</h2>
            <p className="text-xl mb-8 opacity-90">
              No credit card required. Get started in less than 2 minutes.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <button className="bg-white text-black px-8 py-4 rounded-2xl text-lg font-semibold hover:bg-gray-100 transition-all duration-200 transform hover:scale-105 active:scale-95 flex items-center space-x-2">
                <span>Start Free Trial</span>
                <span>✨</span>
              </button>

              <a
                href="/chat"
                className="text-white hover:text-gray-300 transition-colors flex items-center space-x-2 text-lg"
              >
                <span>Or explore the chat playground</span>
                <span>→</span>
              </a>
            </div>
          </div>
        </div>

        {/* Bottom spacing */}
        <div className="h-16"></div>
      </div>
    </DashboardLayout>
  );
};

// Booking Page
const BookingPage = () => (
  <DashboardLayout>
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-black">Booking Management</h1>
        <p className="text-gray-600 mt-2">Manage your appointments and time slots</p>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <p className="text-gray-500 text-center py-12">
          Booking functionality will be implemented here.<br />
          This will connect to the /api/booking endpoints.
        </p>
      </div>
    </div>
  </DashboardLayout>
);

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          {/* Auth routes */}
          <Route path="/login" element={<LoginPage />} />
          <Route path="/signup" element={<SignupPage />} />

          {/* Protected routes */}
          <Route path="/dashboard" element={
            <ProtectedRoute>
              <DashboardPage />
            </ProtectedRoute>
          } />
          <Route path="/chat" element={
            <ProtectedRoute>
              <ChatPlayground />
            </ProtectedRoute>
          } />
          <Route path="/booking" element={
            <ProtectedRoute>
              <BookingPage />
            </ProtectedRoute>
          } />
          <Route path="/cta" element={
            <ProtectedRoute>
              <CTAPage />
            </ProtectedRoute>
          } />

          {/* Default redirect */}
          <Route path="/" element={<Navigate to="/login" replace />} />

          {/* Catch all - redirect to login */}
          <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
